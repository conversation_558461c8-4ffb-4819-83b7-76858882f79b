---
applyTo: '**'
---

# Project Coding Guidelines

This document provides comprehensive coding standards for AI assistants working on this project.

## Project Overview

This project (`@kdt-farm/solana-grpc-client`) is a TypeScript library that implements clients for multiple Solana gRPC services:

- YellowStone Geyser gRPC
- Corvus ARPC
- And more...

The library is designed to provide a type-safe and easy-to-use interface for interacting with these Solana gRPC services.

## Tech Stack

- **Node.js LTS** - Runtime environment (>=22.15.0)
- **TypeScript** - Programming language with static typing
- **PNPM** - Package manager
- **@kdt310722/tsconfig** - TypeScript configuration
- **@kdt310722/eslint-config** - ESLint configuration and style enforcement
- **gRPC** - Remote procedure call framework
- **Protobuf** - Protocol Buffers for serialization
- **ts-proto** - TypeScript code generation from proto files
- **tsup** - TypeScript bundler for building ESM/CJS outputs

## Proto Files and gRPC Clients

### Working with Proto Files

- Proto files are managed as git submodules in the `protos/` directory
- Never modify the proto files directly as they are maintained by their respective projects
- To generate TypeScript code from proto files, use the provided scripts:
    - `pnpm proto:generate` - Generate all clients
    - `pnpm proto:generate:yellowstone` - Generate only YellowStone client
    - `pnpm proto:generate:corvus` - Generate only Corvus client

### Client Implementation Guidelines

- Keep client implementations in their respective directories under `src/clients/`
- Do not modify generated code in the `generated/` directories
- Create wrapper classes that provide a more user-friendly API around the generated clients
- Export client implementations from the respective `index.ts` files
- Use stream wrappers for handling gRPC streaming connections
- Implement proper error handling and connection management for gRPC clients

## TypeScript Configuration

### Project-Specific Settings

- Target ES modules (`"type": "module"` in package.json)
- Use strict TypeScript configuration from `@kdt310722/tsconfig`

### Type Safety Rules

- Use TypeScript's strict type checking
- Prefer `unknown` over `any` type
- Use type-only imports: `import type { Type }` for types only
- Prefer inline type imports: `import { type Type, value }` when mixing
- Define clear types for functions and variables
- Only specify return types for complex types or when not obvious from code
- Use utility types (`Pick`, `Omit`, `Partial`) for type manipulation
- Extract nested interfaces into separate interfaces for reusability

## Code Formatting

### Indentation & Spacing

- Use 4 spaces for indentation, never tabs
- No enforced maximum line length
- Remove trailing whitespace
- Add blank line at end of files
- Use LF (`\n`) line endings
- Place spaces inside object braces: `{ like: this }`
- Add space before opening braces: `function name() {`

### Punctuation & Symbols

- No semicolons at end of statements
- Use single quotes (`'`) for strings in JS/TS
- Use double quotes (`"`) for JSX attributes
- Use trailing commas in ES5 style:
    - Always for multiline arrays and objects
    - Never for imports/exports
    - Never for function parameters
- Always use parentheses with arrow functions: `(param) => {}`
- Use "one true brace style": opening brace on same line
- Closing bracket on new line
- Empty arrow function bodies on single line: `() => {}`

### Line Breaking & Padding

- Add blank lines before and after major code blocks
- Add blank line before `return` statements
- Always blank line before and after: `class`, `interface`, `function`, `if`, `for`, `while`, `switch`, `try`
- No blank lines between `case` statements in `switch`
- Add blank line between variable assignment and subsequent method calls

## Import Organization

### Import Order (Strict)

1. Node.js built-in modules (with `node:` prefix)
2. External libraries (alphabetical)
3. Side-effect imports (`import 'module'`)
4. Internal modules (by proximity: `../`, `./`)

### Import Rules

- Remove unused imports automatically
- Keep import statements at top of file
- Keep each import on one line (no line breaks in import statements)
- Keep export statements on one line without line breaks
- No import type side effects

## Function & Variable Rules

### Function Guidelines

- Keep return statements clear and explicit
- Maximum 30 lines per function (recommended)
- Maximum 3 levels of nesting depth
- Prefix unused parameters with underscore: `_error`, `_unused`
- Use descriptive names indicating purpose
- Prefer arrow functions with direct return for simple transformations
- Keep entire return expressions on one line when possible
- Extract complex inline objects to variables for readability
- For async functions returning single awaited expression, return directly
- Omit `await` in direct returns if not needed for error handling
- **Function signatures should be on single lines when possible** for better readability
- Use concise forms for simple conditional logic (e.g., logical OR operator for simple conditions)
- Prefer single-line implementations for simple timeout/conditional logic
- **Prefer single-line returns for simple method implementations** - When a method simply calls another function or returns a direct expression, use single-line returns without intermediate variables
- **Use consistent parameter destructuring patterns** - When functions accept options objects, use destructuring with default values in the parameter signature (e.g., `{ option1 = default1, option2 = default2 } = {}`) for consistency across the codebase

### Variable Rules

- Use camelCase for variables and functions
- Use PascalCase for classes and components
- Use UPPERCASE_SNAKE_CASE for global constants
- Keep function parameters on same line when reasonable
- Group variable declarations by type (`let` vs `const`) with blank lines between groups
- Apply grouping only to single-line declarations (multi-line declarations remain separate)

## Naming Conventions

### File & Directory Naming

- Files: kebab-case for regular files, PascalCase for component files
- Directories: kebab-case grouped by functionality
- TypeScript files: `.ts` extension
- Generated files: maintain original naming from proto generation

### Code Naming

- Variables and functions: camelCase
- Classes and interfaces: PascalCase
- Constants: UPPERCASE_SNAKE_CASE
- Private class fields: prefer `#privateField` syntax

## Code Organization

### File Structure

1. Imports (following grouping rules)
2. Type definitions and interfaces
3. Constants and configuration
4. Implementation (functions, classes)
5. Exports (prefer named exports, alphabetically organized)

### Module Organization

- Each client has its own directory under `src/clients/`
- Each module exports through `index.ts` file
- Use `export * from './file'` pattern for re-exports
- Use `export type * from './types'` for type-only exports
- Group related functionality in the same module

### Export Patterns

- Use named exports over default exports
- Export types separately: `export type * from './types'`
- Main index exports modules as namespaces when appropriate
- Each module index re-exports all functionality from its files

### Class Organization

Structure class members in this order:

1. Public properties
2. Protected properties
3. Private properties (prefer `#privateField` syntax)
4. Constructor
5. Static methods
6. Instance methods (public → protected → private)

#### Class Formatting Rules

- Add blank lines between access modifier groups
- Group properties by access modifier with spacing
- Prefer inline access modifiers for simple constructors: `public constructor(private readonly config: Config) {}`
- Keep constructor declarations on single line when reasonable

## Code Quality Guidelines

### Performance Considerations

- Use appropriate data structures for the use case
- Avoid unnecessary async/await in direct returns
- Prefer native array methods over manual loops when appropriate
- Optimize gRPC stream handling for better performance

### Error Handling

- Use proper error types and messages
- Handle edge cases gracefully
- Validate input parameters when necessary
- Implement proper error handling for gRPC connections and streams
- Handle stream disconnections and reconnections gracefully

### gRPC and Stream Specific Guidelines

- Use stream wrappers for consistent error handling and event management
- Implement proper cleanup for gRPC streams and connections
- Handle backpressure and flow control in streaming scenarios
- Use appropriate timeouts for gRPC calls
- Implement heartbeat mechanisms for long-lived connections

## Implementation Guidelines

### Scope Limitation Rule

- **Only implement exactly what is requested in the task**
- Do not create additional code, features, or functionality beyond the explicit requirements
- If you think there might be related improvements or additions, ask the user first before implementing
- Focus on the specific requirements and avoid scope creep
- Stick to the minimal viable implementation that satisfies the request

### Default Exclusions Rule

By default, **skip the following unless explicitly requested**:

- **Input validation and parameter checking** - Only add when specifically asked
- **Documentation** - Comments, JSDoc, README updates, or inline documentation
- **Example implementations** - Only create when the user asks for examples

**Important**: Only create these items when the user specifically asks for them in their request. This keeps implementations focused and prevents unnecessary work.

### Code Documentation Rule

- **Never generate comments in source code by default** - Code should be self-documenting through clear naming and structure
- Only add comments when explicitly requested or when code behavior is genuinely non-obvious
- Prefer refactoring unclear code over adding explanatory comments
- Use meaningful variable and function names that eliminate the need for comments

### Code Quality Checks Rule

- **Never run automated checks unless explicitly requested** - Do not automatically run ESLint, TypeScript checks, or other validation tools
- Only execute `pnpm lint`, `pnpm typecheck`, or similar commands when the user specifically asks for them
- Focus on implementing the requested changes without automatic validation unless verification is requested
- Let the user decide when to run quality checks and validation

## Code Reuse Guidelines

### Reuse Principles

- Follow Open/Closed principle: extend without modifying existing code
- Prefer composition over inheritance
- Extract reusable logic into utility functions
- Maintain backward compatibility when extending functionality

### Avoiding Duplication

- Follow DRY (Don't Repeat Yourself) principle
- Apply Rule of Three: if code is copy-pasted 3 times, extract it into reusable function
- Search existing utilities before implementing new functionality

### Maintainability

- Keep functions focused on single responsibility
- Use meaningful variable and function names
- Maintain consistent code organization across modules
- Use TypeScript's type system to prevent runtime errors
