{"name": "@kdt-farm/solana-grpc-client", "type": "module", "version": "0.0.1", "packageManager": "pnpm@10.11.0", "description": "Typescript client implements for multiple Solana GRPC services (YellowStone Geyser GRPC, Corvus ARPC,...)", "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "homepage": "https://github.com/kdt-farm/solana-grpc-client", "repository": "github:kdt-farm/solana-grpc-client", "bugs": {"email": "<EMAIL>", "url": "https://github.com/kdt-farm/solana-grpc-client/issues"}, "sideEffects": false, "exports": {".": {"types": "./dist/types/index.d.ts", "require": "./dist/index.cjs", "default": "./dist/index.js"}, "./corvus": {"types": "./dist/types/clients/corvus/index.d.ts", "require": "./dist/clients/corvus/index.cjs", "default": "./dist/clients/corvus/index.js"}, "./clients/corvus": {"types": "./dist/types/clients/corvus/index.d.ts", "require": "./dist/clients/corvus/index.cjs", "default": "./dist/clients/corvus/index.js"}, "./yellowstone": {"types": "./dist/types/clients/yellowstone/index.d.ts", "require": "./dist/clients/yellowstone/index.cjs", "default": "./dist/clients/yellowstone/index.js"}, "./clients/yellowstone": {"types": "./dist/types/clients/yellowstone/index.d.ts", "require": "./dist/clients/yellowstone/index.cjs", "default": "./dist/clients/yellowstone/index.js"}}, "main": "dist/index.js", "types": "dist/types/index.d.ts", "files": ["dist", "src"], "publishConfig": {"access": "public"}, "scripts": {"build": "rimraf dist && tsup && tsc --project ./tsconfig.build.json", "proto:generate": "pnpm run proto:generate:yellowstone && pnpm run proto:generate:corvus && pnpm run proto:generate:shred-forwarder", "proto:generate:yellowstone": "rm -rf ./src/clients/yellowstone/generated && mkdir -p ./src/clients/yellowstone/generated && protoc --plugin=./node_modules/.bin/protoc-gen-ts_proto --ts_proto_opt=outputServices=grpc-js,forceLong=bigint,env=node,esModuleInterop=true,exportCommonSymbols=false,useAbortSignal=true,useAsyncIterable=true --experimental_allow_proto3_optional --ts_proto_out=./src/clients/yellowstone/generated --proto_path=./protos/yellowstone/yellowstone-grpc-proto/proto ./protos/yellowstone/yellowstone-grpc-proto/proto/*.proto", "proto:generate:corvus": "rm -rf ./src/clients/corvus/generated && mkdir -p ./src/clients/corvus/generated && protoc --plugin=./node_modules/.bin/protoc-gen-ts_proto --ts_proto_opt=outputServices=grpc-js,forceLong=bigint,env=node,esModuleInterop=true,exportCommonSymbols=false,useAbortSignal=true,useAsyncIterable=true --experimental_allow_proto3_optional --ts_proto_out=./src/clients/corvus/generated --proto_path=./protos/corvus/clients/rust/proto ./protos/corvus/clients/rust/proto/*.proto", "proto:generate:shred-forwarder": "rm -rf ./src/clients/shred-forwarder/generated && mkdir -p ./src/clients/shred-forwarder/generated && protoc --plugin=./node_modules/.bin/protoc-gen-ts_proto --ts_proto_opt=outputServices=grpc-js,forceLong=bigint,env=node,esModuleInterop=true,exportCommonSymbols=false,useAbortSignal=true,useAsyncIterable=true --experimental_allow_proto3_optional --ts_proto_out=./src/clients/shred-forwarder/generated --proto_path=./protos/shred-forwarder ./protos/shred-forwarder/*.proto", "release": "tsx scripts/release.ts && changelogen gh release && pnpm publish", "up": "ncu -i", "lint": "eslint .", "lint:fix": "eslint . --fix", "typecheck": "tsc --noEmit", "preinstall": "npx only-allow pnpm", "prepare": "simple-git-hooks", "prepublishOnly": "pnpm build"}, "dependencies": {"@bufbuild/protobuf": "^2.5.1", "@grpc/grpc-js": "^1.13.4", "@kdt310722/utils": "^0.0.19"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@kdt310722/eslint-config": "^0.2.0", "@kdt310722/tsconfig": "^1.0.0", "@swc/core": "^1.11.29", "@types/node": "^22.15.26", "changelogen": "^0.6.1", "eslint": "^9.27.0", "execa": "^9.6.0", "lint-staged": "^16.1.0", "npm-check-updates": "^18.0.1", "only-allow": "^1.2.1", "rimraf": "^6.0.1", "simple-git-hooks": "^2.13.0", "ts-proto": "^2.7.1", "tsup": "^8.5.0", "tsx": "^4.19.4", "typescript": "^5.8.3"}, "commitlint": {"extends": "@commitlint/config-conventional"}, "simple-git-hooks": {"commit-msg": "npx --no -- commitlint --edit ${1}", "pre-commit": "npx lint-staged"}, "lint-staged": {"*": "eslint --fix"}}