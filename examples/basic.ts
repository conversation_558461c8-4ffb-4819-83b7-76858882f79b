import { stringifyError } from '@kdt310722/utils/error'
import { formatNanoseconds, isBigInt } from '@kdt310722/utils/number'
import { ShredForwarderClient } from '../src/clients/shred-forwarder'

const endpoint = 'http://46.203.233.73:50051'

const getErrorMessage = (error: unknown) => {
    if (error instanceof Error) {
        return stringifyError(error)
    }

    return error
}

const stringifyJson = (data: unknown) => {
    return JSON.stringify(data, (_, value) => (isBigInt(value) ? value.toString() : value))
}

async function run() {
    const client = new ShredForwarderClient(endpoint)

    console.log('Ping:', await client.ping({ count: 0 }))

    const stream = client.createStream()

    process.on('SIGINT', () => {
        console.log('SIGINT received, closing stream...')

        stream.close().catch((error) => {
            console.error('Error while closing stream:', getErrorMessage(error))
            process.exit(1)
        })

        client.grpc.close()
    })

    stream.on('data', (data) => console.log('Data received:', stringifyJson(data)))
    stream.on('state', (state) => console.log('State:', state))
    stream.on('error', (error) => console.error('Stream error:', getErrorMessage(error)))
    stream.on('closed', () => console.log('Stream closed!'))
    stream.on('waitForResubscribe', (delay) => console.log(new Date().toISOString(), `Will resubscribe in ${formatNanoseconds(BigInt(delay * 1e6))}`))
    stream.on('resubscribe', (attempt, retriesLeft) => console.log(new Date().toISOString(), 'Stream resubscribe:', 'attempt', attempt, 'retriesLeft', retriesLeft))
    stream.on('circuitBreakerTripped', (lastResubscribeSuccessTime) => console.log('Circuit breaker tripped:', new Date(lastResubscribeSuccessTime).toISOString()))
    stream.on('resubscribed', () => console.log('Stream resubscribed!'))
    stream.on('wrote', (data) => console.log('Stream wrote:', stringifyJson(data)))

    stream.on('resubscribeAbandoned', (reason) => {
        console.log('Resubscribe Abandoned:', reason)
        process.exit(1)
    })

    await stream.subscribe().then(() => {
        console.log('Stream subscribed!')
    })
}

run().catch((error) => {
    console.error(getErrorMessage(error))
    process.exit(1)
})
